<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\Department as DepartmentModel;

class Department extends BaseController
{
    

    /**
     * 获取部门树形结构
     * @return \think\response\Json
     */
    public function index()
    {
        try {
            // 获取租户ID
            $tenantId = $this->request->tenant_id;

            // 实例化部门模型
            $departmentModel = new DepartmentModel();

            // 获取部门树形数据
            $tree = $departmentModel->getDepartmentTree($tenantId);

            return $this->success($tree, '获取部门树形结构成功');

        } catch (\Exception $e) {
            return $this->error('获取部门树形结构失败：' . $e->getMessage());
        }
    }

    /**
     * 添加部门
     * @return \think\response\Json
     */
    public function add()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateDepartmentData($data);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 获取租户ID
            $tenantId = $this->request->tenant_id;

            // 实例化部门模型
            $departmentModel = new DepartmentModel();

            // 添加部门
            $departmentId = $departmentModel->addDepartment($data, $tenantId);

            if ($departmentId) {
                return $this->success(['department_id' => $departmentId], '部门添加成功');
            } else {
                return $this->error('部门添加失败');
            }

        } catch (\Exception $e) {
            return $this->error('部门添加失败：' . $e->getMessage());
        }
    }

    /**
     * 编辑部门
     * @return \think\response\Json
     */
    public function edit()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取部门ID
            $departmentId = (int)$this->request->param('department_id', 0);
            if (!$departmentId) {
                return $this->error('部门ID不能为空');
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateDepartmentData($data, false);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 获取租户ID
            $tenantId = $this->request->tenant_id;

            // 实例化部门模型
            $departmentModel = new DepartmentModel();

            // 编辑部门
            $result = $departmentModel->editDepartment($departmentId, $data, $tenantId);

            if ($result) {
                return $this->success(['department_id' => $departmentId], '部门编辑成功');
            } else {
                return $this->error('部门编辑失败');
            }

        } catch (\Exception $e) {
            return $this->error('部门编辑失败：' . $e->getMessage());
        }
    }

    /**
     * 删除部门
     * @return \think\response\Json
     */
    public function delete()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取部门ID
            $departmentId = (int)$this->request->param('department_id', 0);
            if (!$departmentId) {
                return $this->error('部门ID不能为空');
            }

            // 获取租户ID
            $tenantId = $this->request->tenant_id;

            // 实例化部门模型
            $departmentModel = new DepartmentModel();

            // 删除部门
            $result = $departmentModel->deleteDepartment($departmentId, $tenantId);

            if ($result) {
                return $this->success(['department_id' => $departmentId], '部门删除成功');
            } else {
                return $this->error('部门删除失败');
            }

        } catch (\Exception $e) {
            return $this->error('部门删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取部门详情
     * @return \think\response\Json
     */
    public function detail()
    {
        try {
            // 获取部门ID
            $departmentId = (int)$this->request->param('department_id', 0);
            if (!$departmentId) {
                return $this->error('部门ID不能为空');
            }

            // 获取租户ID
            $tenantId = $this->request->tenant_id;

            // 实例化部门模型
            $departmentModel = new DepartmentModel();

            // 获取部门详情
            $department = $departmentModel->getDepartmentDetail($departmentId, $tenantId);
            if (!$department) {
                return $this->error('部门不存在');
            }

            return $this->success($department, '获取部门详情成功');

        } catch (\Exception $e) {
            return $this->error('获取部门详情失败：' . $e->getMessage());
        }
    }

    /**
     * 验证部门数据
     * @param array $data 部门数据
     * @param bool $isAdd 是否为添加操作
     * @return string|true 验证结果，true表示通过，字符串表示错误信息
     */
    private function validateDepartmentData($data, $isAdd = true)
    {
        // 部门名称验证
        if ($isAdd && empty($data['department_name'])) {
            return '部门名称不能为空！';
        }

        if (!empty($data['department_name']) && mb_strlen($data['department_name']) > 50) {
            return '部门名称不能超过50个字符！';
        }

        // 父级部门验证
        if (!empty($data['parent_id']) && !is_numeric($data['parent_id'])) {
            return '父级部门ID格式不正确！';
        }

        // 排序验证
        if (!empty($data['sort_order']) && !is_numeric($data['sort_order'])) {
            return '排序值必须为数字！';
        }

        // 状态验证
        if (isset($data['status']) && !in_array($data['status'], [0, 1])) {
            return '状态值不正确！';
        }



        return true;
    }
}
